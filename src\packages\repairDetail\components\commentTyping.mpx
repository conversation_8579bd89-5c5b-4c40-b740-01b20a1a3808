<template>
  <t-popup
    usingCustomNavbar
    overlay-props="{{ {zIndex: 998} }}"
    visible="{{visible}}"
    bind:visible-change="onClose"
    placement="{{bottom}}"
  >
    <view
      class="expanded"
      style="padding-bottom: {{keyboardHeight}}px; transition: padding-bottom 0.3s ease-in-out;"
    >
      <textarea
        class="comment-textarea"
        placeholder="{{placeholder}}"
        value="{{inputValue}}"
        bindinput="handleInput"
        focus="{{shouldFocus}}"
        maxlength="500"
        style="height: {{textareaHeight}}px; transition: height 0.3s ease-in-out;"
        adjust-position="{{false}}"
        bindfocus="handleTextareaFocus"
        bindblur="handleTextareaBlur"
      />
      <view class="expanded-actions">
        <uploader
          maxFiles="10"
          bind:uploadComplete="onUploadComplete"
          bind:previewInfoChange="onPreviewInfoChange"
          previewHeight="{{previewHeight}}"
          class="uploader-wrapper"
        >
          <view slot="upload-trigger">
            <image src="{{TechMaintainUrl.UploadFile}}" class="action-icon" />
          </view>
        </uploader>
        <view class="right-actions">
          <view class="cancel expanded-btn" bindtap="handleCancel"
            >取消回复</view
          >
          <view class="send expanded-btn" bindtap="handleSendComment"
            >发送</view
          >
        </view>
      </view>
    </view>
  </t-popup>
</template>

<script lang="ts">
  import { createComponent } from '@mpxjs/core';
  import { TechMaintainUrl } from 'shared/assets/imageUrl';
  // 高度常量定义
  const MIN_TEXT_HEIGHT = 80; // 文本区域最小高度(rpx)
  const MAX_TEXT_HEIGHT = 130; // 文本区域最大高度(rpx)
  const MAX_PREVIEW_HEIGHT = 150; // 预览区最大高度(rpx)
  const MIN_PREVIEW_HEIGHT = 0; // 预览区最小高度(rpx)
  const LINE_HEIGHT = 16; // 单行高度(rpx)
  const CHARS_PER_LINE = 20; // 每行字符数
  const systemInfo = wx.getSystemInfoSync();
  const windowHeight = systemInfo.windowHeight;
  const statusBarHeight = systemInfo.statusBarHeight || 0;
  createComponent({
    properties: {
      visible: {
        type: Boolean,
        value: false // 是否展开输入框
      },
      replyTo: {
        type: Object,
        value: {}
      }
    },
    data: {
      TechMaintainUrl,

      inputValue: '',
      placeholder: '输入留言',
      shouldFocus: false, // 控制聚焦状态

      textareaHeight: MIN_TEXT_HEIGHT, // 展开输入框的默认高度
      previewHeight: MIN_PREVIEW_HEIGHT, // 初始预览高度
      maxContentHeight: 0, // 最大可用高度
      keyboardHeight: 0, // 键盘高度
      mediaCount: 0, // 媒体文件数量
      otherCount: 0, // 其他文件数量
      isUploading: false, // 是否有文件正在上传

      successFiles: [] // 上传成功的文件列表
    },
    lifetimes: {
      attached() {
        wx.onKeyboardHeightChange((res: any) => {
          const newKeyboardHeight = res.height;
          const maxContentHeight = this.calculateMaxHeight(newKeyboardHeight);
          const contentHeight =
            this.data.textareaHeight + this.data.previewHeight;
          this.setData({
            keyboardHeight: newKeyboardHeight
          });
          if (newKeyboardHeight > 0) {
            this.adjustForKeyboardOpen(maxContentHeight, contentHeight);
          } else {
            this.adjustForKeyboardClose(maxContentHeight);
          }
        });
      },
      detached() {
        wx.offKeyboardHeightChange();
      },
      ready() {
        this.calculateMaxHeight();
      }
    },
    watch: {
      replyTo(newVal: any) {
        if (newVal && newVal.userName) {
          this.setData({
            placeholder: `回复 ${newVal.userName}`
          });
          setTimeout(() => {
            this.setData({
              shouldFocus: true
            });
          }, 100);
        } else {
          this.setData({
            placeholder: '输入留言',
            inputValue: '',
            shouldFocus: false,
            textareaHeight: MIN_TEXT_HEIGHT
          });
        }
      }
    },
    methods: {
      onClose() {
        console.log('关闭弹窗');
        this.triggerEvent('close');
      },
      calculateMaxHeight(keyboardHeight?: number) {
        keyboardHeight = keyboardHeight || this.data.keyboardHeight;
        const availableHeight =
          (windowHeight - statusBarHeight - keyboardHeight) * 0.4;
        this.setData({
          maxContentHeight: availableHeight
        });
        return availableHeight;
      },
      handleCancel() {
        this.setData({
          inputValue: '',
          placeholder: '输入留言',
          textareaHeight: MIN_TEXT_HEIGHT,
          previewHeight: MIN_PREVIEW_HEIGHT
        });
        this.onClose();
        this.triggerEvent('cancelReply');
      },
      handleInput(event: any) {
        const value = event.detail.value;
        this.setData({
          inputValue: value
        });
        this.adjustTextareaHeight(value);
      },
      handleSendComment() {
        const content = this.data.inputValue.trim();
        if (!content) {
          wx.showToast({
            title: '请输入留言内容',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        this.triggerEvent('sendComment', {
          content,
          replyTo: this.data.replyTo,
          successFiles: this.data.successFiles
        });
        this.setData({
          inputValue: '',
          placeholder: '输入留言',
          textareaHeight: MIN_TEXT_HEIGHT,
          previewHeight: MIN_PREVIEW_HEIGHT
        });
      },
      adjustForKeyboardOpen(maxHeight: number, contentHeight: number) {
        if (contentHeight <= maxHeight) return;
        const overflow = contentHeight - maxHeight;
        // 优先文本压缩
        // 计算文本区域最多可压缩的空间
        const textReducibleSpace = this.data.textareaHeight - MIN_TEXT_HEIGHT;
        let newTextHeight = this.data.textareaHeight;
        let newPreviewHeight = this.data.previewHeight;
        if (textReducibleSpace >= overflow) {
          // 文本区域可以完全压缩溢出的高度
          newTextHeight = this.data.textareaHeight - overflow;
        } else {
          // 文本区域压缩到最小高度
          newTextHeight = MIN_TEXT_HEIGHT;
          // 剩余溢出空间由预览区压缩
          const remainingOverflow = overflow - textReducibleSpace;
          newPreviewHeight = Math.max(
            MIN_PREVIEW_HEIGHT,
            this.data.previewHeight - remainingOverflow
          );
        }
        this.setData({
          previewHeight: Math.max(MIN_PREVIEW_HEIGHT, newTextHeight),
          textareaHeight: Math.max(MIN_TEXT_HEIGHT, newTextHeight)
        });
      },
      adjustForKeyboardClose(maxHeight: number) {
        this.adjustTextareaHeight(this.data.inputValue);
        setTimeout(() => {
          this.adjustPreviewHeight();
        }, 300);
      },
      adjustTextareaHeight(text: string) {
        if (!text) {
          this.setData({ textareaHeight: MIN_TEXT_HEIGHT });
          return;
        }
        // 计算理想的文本区域高度
        const textLength = text.replace(/[\u4e00-\u9fa5]/g, 'aa').length;
        const estimatedLines = Math.ceil(textLength / CHARS_PER_LINE);
        let idealTextareaHeight = estimatedLines * LINE_HEIGHT;
        // 应用最小/最大高度限制
        idealTextareaHeight = Math.max(MIN_TEXT_HEIGHT, idealTextareaHeight);
        idealTextareaHeight = Math.min(MAX_TEXT_HEIGHT, idealTextareaHeight);
        const availableForText = Math.max(
          MIN_TEXT_HEIGHT,
          this.data.maxContentHeight - this.data.previewHeight
        );
        const finalHeight = Math.min(idealTextareaHeight, availableForText);
        this.setData({ textareaHeight: finalHeight });
      },
      adjustPreviewHeight() {
        // 计算理想预览高度
        let idealPreviewHeight = this.calculatePreviewHeight(
          this.data.mediaCount,
          this.data.otherCount,
          this.data.isUploading
        );
        // 应用最小/最大高度限制
        idealPreviewHeight = Math.max(MIN_PREVIEW_HEIGHT, idealPreviewHeight);
        idealPreviewHeight = Math.min(MAX_PREVIEW_HEIGHT, idealPreviewHeight);
        const availableForPreview = Math.max(
          MIN_PREVIEW_HEIGHT,
          this.data.maxContentHeight - this.data.textareaHeight
        );
        const finalPreviewHeight = Math.min(
          idealPreviewHeight,
          availableForPreview
        );
        this.setData({ previewHeight: finalPreviewHeight });
      },
      calculatePreviewHeight(
        mediaCount: number,
        otherCount: number,
        isUploading: boolean
      ) {
        if (isUploading && mediaCount === 0 && otherCount === 0) return 90;
        const imageHeight = 80;
        const mediaRows = Math.ceil(mediaCount / 4);
        const fileRowHeight = 56;
        const fileRows = Math.ceil(otherCount / 2);
        let height = 0;
        if (mediaCount > 0) {
          height += mediaRows * imageHeight;
        }
        if (otherCount > 0) {
          height += fileRows * fileRowHeight;
        }
        return height;
      },
      onUploadComplete(event: any) {
        const { successFiles } = event.detail;
        this.setData({ successFiles: successFiles || [] });
      },
      onPreviewInfoChange(event: any) {
        const { mediaCount, otherCount, isUploading } = event.detail || {};
        if (
          mediaCount === this.data.mediaCount &&
          otherCount === this.data.otherCount &&
          isUploading === this.data.isUploading
        ) {
          return;
        }
        this.setData({
          mediaCount: mediaCount || 0,
          otherCount: otherCount || 0,
          isUploading: isUploading || false
        });
        this.adjustPreviewHeight();
      }
    }
  });
</script>

<style lang="scss">
  .expanded {
    position: fixed;
    width: 100vw;
    height: auto;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 32rpx 32rpx 64rpx 32rpx;
    z-index: 999;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    .comment-textarea {
      width: 100%;
      padding: 12rpx;
      border: 1rpx solid #e0e0e0;
      border-radius: 16rpx;
      font-size: 32rpx;
      color: #333333;
      background: #f8f8f8;
      box-sizing: border-box;
      resize: none;

      &::placeholder {
        color: #999999;
      }

      &:focus {
        border-color: #fa2c19;
        background: #ffffff;
      }
    }

    .expanded-actions {
      position: relative;
      margin: 16rpx 0 32rpx 0;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      min-height: 72rpx;

      .uploader-wrapper {
        flex-shrink: 0;
        .action-icon {
          width: 56rpx;
          height: 56rpx;
          display: block;
          transition: all 0.2s ease;
          object-fit: contain;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;

          &:active {
            transform: scale(0.95);
          }
        }
      }

      .right-actions {
        display: flex;
        align-items: center;
        height: 72rpx;
        font-size: 32rpx;
        .expanded-btn {
          padding: 12rpx 24rpx;
          border-radius: 32rpx;
          &.cancel {
            background: #fff;
            border: 2rpx solid #fa3619;
            color: #fa3619;
            margin-right: 8rpx;
          }
          &.send {
            background: #fa3619;
            border: 2rpx solid #fff;
            color: #ffffff;
            font-weight: 500;
          }
        }
      }
    }
  }
</style>

<script type="application/json">
  {
    "component": true,
    "usingComponents": {
      "uploader": "shared/ui/uploader.mpx",
      "t-popup": "tdesign-miniprogram/popup/popup"
    }
  }
</script>